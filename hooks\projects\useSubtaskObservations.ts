"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";

export interface SubtaskObservation {
  subtask_name: string;
  observation: string;
}

export interface UpdateSubtaskObservationsRequest {
  field_id: number;
  subtask_observations: SubtaskObservation[];
}

export function useSubtaskObservations() {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateSubtaskObservations = async (
    projectId: string | number,
    phaseId: number,
    data: UpdateSubtaskObservationsRequest,
  ) => {
    try {
      setUpdating(true);
      setError(null);

      const url = API_ROUTES.UPDATE_SUBTASK_OBSERVATIONS
        .replace("{project_id}", String(projectId))
        .replace("{phase_id}", String(phaseId));

      const response = await axiosInstance.post(url, data);

      if (response.status === 200) {
        return response.data;
      } else {
        throw new Error("Failed to update subtask observations");
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || "Failed to update subtask observations";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setUpdating(false);
    }
  };

  return {
    updateSubtaskObservations,
    updating,
    error,
  };
}
