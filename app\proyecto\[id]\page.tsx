"use client";

import type { Selection } from "@heroui/react";

import { FaEnvelope, FaTasks } from "react-icons/fa";
import React, { useState, useMemo, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Card, CardBody, Chip, Tooltip, Skeleton } from "@heroui/react";
import { Accordion, AccordionItem } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";
import { useParams } from "next/navigation";

import { ProgressTimeline } from "@/components/projects/project/progress-timeline";
import { getPhaseStyle, title } from "@/components/primitives";
import Field from "@/components/fields/field";
import { ContactsModal } from "@/components/contacts/contacts-modal";
import { Subphase } from "@/utils/dummy-data";
import { CommentsSection } from "@/components/comments/comments-section";
import { FieldProps } from "@/types/fields";
import { TasksModal } from "@/components/projects/project/tasks-modal";
import { VerificationModal } from "@/components/projects/project/verification-modal";
import { useProject } from "@/hooks/projects/useProject";
import { useProjectFields } from "@/hooks/projects/useProjectFields";
import {
  transformApiSubphasesToComponent,
  getPhaseIdByName,
  mapApiPhaseToComponent,
  transformComponentFieldsToApi,
} from "@/utils/project-transformers";
import { usePendingFields } from "@/hooks/fields/usePendingFields";
import { useAuth } from "@/hooks/auth/useAuth";
import { ReportModal } from "@/components/reports/reportModal";
import { NotificationsModal } from "@/components/notifications/notifications-modal";
import { useSubtaskObservations } from "@/hooks/projects/useSubtaskObservations";

export default function ProyectoPage() {
  // All hooks must be at the top level
  const { theme } = useTheme();
  const params = useParams();
  const projectId = params.id as string;

  const {
    projectData,
    loading: projectLoading,
    error: projectError,
    refetch: refetchProject,
  } = useProject(projectId);

  const { hasPermission } = useAuth();
  const { pendingFields, fetchPendingFields } = usePendingFields();
  const { updateSubtaskObservations } = useSubtaskObservations();

  // State hooks
  const [canVerifyPhase, setCanVerifyPhase] = useState(false);
  const [canModifyFields, setCanModifyFields] = useState(false);
  const [canViewContacts, setCanViewContacts] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [mailModalOpen, setMailModalOpen] = useState(false);
  const [tasksModalOpen, setTasksModalOpen] = useState(false);
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState<string>("");
  const [selectedSubphases, setSelectedSubphases] = useState<Set<string>>(
    new Set(["1"]),
  );
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [cancelEditTrigger, setCancelEditTrigger] = useState(false);
  const [filterMilestones, setFilterMilestones] = useState(false);
  const [filterCompleted, setFilterCompleted] = useState(false);
  const [updatedFields, setUpdatedFields] = useState<Record<number, string>>(
    {},
  );
  const [updatedObservations, setUpdatedObservations] = useState<
    Record<number, string>
  >({});

  useEffect(() => {
    setCanVerifyPhase(hasPermission("verificar_fase"));
    setCanModifyFields(hasPermission("editar_campos"));
    setCanViewContacts(hasPermission("visualizar_contactos"));
  }, []);

  // Memoized values
  const currentPhaseId = useMemo(() => {
    if (!projectData?.phases) return null;
    const phaseId = getPhaseIdByName(projectData.phases, selectedPhase);

    return phaseId === -1 ? null : phaseId;
  }, [projectData?.phases, selectedPhase]);

  const { actualCurrentPhase, currentPhaseName, tempState } = useMemo(() => {
    if (!projectData)
      return { actualCurrentPhase: null, currentPhaseName: "", tempState: "" };

    const sortedPhases = projectData.phases.sort((a, b) => a.order - b.order);
    const actualCurrentPhase =
      sortedPhases.find((phase) => !phase.completed) || sortedPhases[0];
    const currentPhaseName = mapApiPhaseToComponent(actualCurrentPhase.name);
    const tempState = projectData.project.status;

    return { actualCurrentPhase, currentPhaseName, tempState };
  }, [projectData]);

  // useCallback hooks
  const handleFieldChange = useCallback(
    (id: number, value: string, observation?: string) => {
      setUpdatedFields((prev) => ({
        ...prev,
        [id]: value,
      }));
      if (observation !== undefined) {
        setUpdatedObservations((prev) => ({
          ...prev,
          [id]: observation,
        }));
      }
    },
    [],
  );

  const handleSubtaskObservationsUpdate = useCallback(
    async (fieldId: number, subtaskObservations: { [key: string]: string }, subtasks: any[]) => {
      try {
        if (!currentPhaseId) return;

        // Convert subtask observations to the required API format
        // The key in subtaskObservations is the subtask index, we need to map it to the actual subtask title
        const subtaskObservationsArray = Object.entries(subtaskObservations).map(
          ([subtaskIndex, observation]) => {
            const subtask = subtasks[parseInt(subtaskIndex)];
            return {
              subtask_name: subtask?.title || subtaskIndex,
              observation: observation,
            };
          },
        );

        await updateSubtaskObservations(projectId, currentPhaseId, {
          field_id: fieldId,
          subtask_observations: subtaskObservationsArray,
        });
      } catch (error) {
        console.error("Error updating subtask observations:", error);
      }
    },
    [projectId, currentPhaseId, updateSubtaskObservations],
  );

  const isFieldCompleted = useCallback((field: FieldProps): boolean => {
    enum TaskState {
      NOT_COMPLETED = "pendiente",
      IN_PROGRESS = "en_progreso",
      COMPLETED = "completado",
    }

    const taskOptions = [
      { key: "pendiente", value: TaskState.NOT_COMPLETED },
      { key: "en_progreso", value: TaskState.IN_PROGRESS },
      { key: "completado", value: TaskState.COMPLETED },
      { key: "no_aplica", value: TaskState.COMPLETED },
    ];

    switch (field.type) {
      case "INFORMATIVE":
      case "SELECTION":
        return !!field.value;
      case "TASK":
      case "DOCUMENT":
        const selectedOption = taskOptions.find(
          (opt) => opt.key === field.value,
        );

        return selectedOption?.value === TaskState.COMPLETED;
      case "TASK_WITH_SUBTASKS":
        return (
          field.subtasks?.every((subtask) => {
            const selectedOption = taskOptions.find(
              (opt) => opt.key === subtask.value,
            );

            return selectedOption?.value === TaskState.COMPLETED;
          }) ?? false
        );
      default:
        return false;
    }
  }, []);

  const MemoizedField = useCallback(
    ({ field }: { field: FieldProps }) => (
      <div key={field.id} id={`field-${field.id}`}>
        <Field
          key={field.id}
          cancelEditTrigger={cancelEditTrigger}
          description={field.description}
          edit={editMode}
          id={field.id}
          milestone={field.milestone}
          observation={field.observation || ""}
          options={field.options}
          subtasks={field.subtasks}
          title={field.title}
          type={field.type}
          value={field.value}
          onChange={handleFieldChange}
          onSubtaskObservationsUpdate={handleSubtaskObservationsUpdate}
        />
      </div>
    ),
    [cancelEditTrigger, editMode, handleFieldChange, handleSubtaskObservationsUpdate],
  );

  // Fields API hook - must come before filteredSubphases
  const {
    fieldsData,
    loading: fieldsLoading,
    updating: fieldsUpdating,
    error: fieldsError,
    updateFields,
  } = useProjectFields(projectId, currentPhaseId);

  const filteredSubphases = useMemo(() => {
    if (!fieldsData || !projectData) {
      return [];
    }

    if (selectedPhase !== fieldsData.phase.name) return [];

    // Transform API data to component format
    return transformApiSubphasesToComponent(
      fieldsData.subphases,
      fieldsData.fields,
      selectedPhase,
    ).sort((a, b) => a.order - b.order);
  }, [fieldsData, projectData, selectedPhase]);

  // All useEffect hooks
  // This was moved to the top of the component

  useEffect(() => {
    if (fieldsData) {
      const savedState = loadAccordionState(
        selectedPhase,
        fieldsData.subphases,
      );

      setSelectedSubphases(savedState);
    }
  }, [fieldsData, selectedPhase]);

  useEffect(() => {
    if (fieldsData) {
      // Load saved state when phase changes
      const savedState = loadAccordionState(
        selectedPhase,
        fieldsData.subphases,
      );

      cancelSave();
      setUpdatedFields({});
      setUpdatedObservations({});

      setSelectedSubphases(savedState);
    }
  }, [selectedPhase, fieldsData]);

  useEffect(() => {
    if (
      projectData?.phases &&
      projectData.phases.length > 0 &&
      !selectedPhase
    ) {
      // Find the first phase with completed: false, sorted by order
      const sortedPhases = projectData.phases.sort((a, b) => a.order - b.order);
      const currentPhase =
        sortedPhases.find((phase) => !phase.completed) || sortedPhases[0];
      const mappedPhaseName = mapApiPhaseToComponent(currentPhase.name);

      setSelectedPhase(mappedPhaseName);
    }
  }, [projectData, selectedPhase]);

  useEffect(() => {
    if (!projectId) return;

    fetchPendingFields(projectId);
  }, [projectId]); // Removed fetchPendingFields to prevent infinite loop

  // Save accordion state to consolidated localStorage
  const saveAccordionState = (phase: string, keys: Set<string>) => {
    if (typeof window !== "undefined") {
      // Get existing state or initialize new object
      const storageKey = "project_accordion_states";
      let allStates: Record<string, Record<string, string[]>> = {};

      try {
        const savedData = localStorage.getItem(storageKey);

        if (savedData) {
          allStates = JSON.parse(savedData);
        }
      } catch (e) {
        console.error("Error parsing saved states:", e);
      }

      // Ensure nested structure exists
      if (!allStates[projectId]) {
        allStates[projectId] = {};
      }

      // Save the state for this phase
      allStates[projectId][phase] = Array.from(keys);

      // Save back to localStorage
      localStorage.setItem(storageKey, JSON.stringify(allStates));
    }
  };

  // Load accordion state from consolidated localStorage
  const loadAccordionState = (
    phase: string,
    subphases?: any[],
  ): Set<string> => {
    if (typeof window !== "undefined") {
      const storageKey = "project_accordion_states";

      try {
        const savedData = localStorage.getItem(storageKey);

        if (savedData) {
          const allStates = JSON.parse(savedData);

          if (allStates[projectId] && allStates[projectId][phase]) {
            return new Set(allStates[projectId][phase]);
          }
        }
      } catch (e) {
        console.error("Error parsing saved accordion state:", e);
      }
    }

    // Return all subphases selected if no saved state
    // Use provided subphases if available, otherwise return default
    if (subphases && subphases.length > 0) {
      return new Set(subphases.map((subphase) => String(subphase.id)));
    }

    return new Set(["1"]); // Default fallback
  };

  const handlePhaseSelect = (phase: string) => {
    // Cancel any ongoing edits
    cancelSave();
    setUpdatedFields({});
    setUpdatedObservations({});

    setSelectedPhase(phase);
    // Load saved state for the new phase will be handled by useEffect
  };

  // Handler for accordion selection change
  const handleSelectionChange = (keys: Selection) => {
    // Convert the Selection type to Set<string>
    if (keys === "all") {
      // If "all" is selected, include all subphase IDs
      const allIds = new Set(
        filteredSubphases.map((subphase) => String(subphase.id)),
      );

      setSelectedSubphases(allIds);
      saveAccordionState(selectedPhase, allIds);
    } else {
      // Otherwise convert the keys object to a Set
      const newKeys = new Set(keys as Set<string>);

      setSelectedSubphases(newKeys);
      saveAccordionState(selectedPhase, newKeys);
    }
  };

  // Function to navigate to a specific task
  const handleViewTask = async (taskId: string) => {
    // Find the task in pending fields
    const task = pendingFields?.find(
      (field: any) => field.field_id === parseInt(taskId),
    );

    // Debug logs removed for performance
    if (!task) {
      console.error("Task not found:", taskId);

      return;
    }

    // Close the tasks modal first
    setTasksModalOpen(false);

    // Get the phase name from the task
    const taskPhaseName = task.phase_name;

    if (!taskPhaseName) {
      console.error("ss! Task phase not found:", task);

      return;
    }

    // Convert phase name using the utility function
    const normalizedPhaseName = mapApiPhaseToComponent(taskPhaseName);

    // Function to handle scrolling and highlighting
    const scrollAndHighlight = () => {
      // Find the subphase that contains this task
      const taskSubphaseId = task.subphase_id;

      if (taskSubphaseId) {
        // Expand the subphase that contains the task
        const subphaseIdString = String(taskSubphaseId);
        const newSelectedSubphases = new Set(selectedSubphases);

        newSelectedSubphases.add(subphaseIdString);
        setSelectedSubphases(newSelectedSubphases);
        saveAccordionState(normalizedPhaseName, newSelectedSubphases);

        // Wait for accordion to expand and then scroll
        setTimeout(() => {
          // Scroll to the specific field
          const fieldElement = document.getElementById(`field-${taskId}`);

          if (fieldElement) {
            fieldElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
              inline: "nearest",
            });

            // Find the Card component inside the field element
            const cardElement = fieldElement.querySelector(
              ".mb-4",
            ) as HTMLElement;

            if (cardElement) {
              // Add CSS class for highlight animation
              cardElement.classList.add("task-highlight");

              // Remove highlight after 3 seconds
              setTimeout(() => {
                cardElement.classList.remove("task-highlight");
                cardElement.classList.add("task-highlight-fade-out");

                // Clean up fade-out class after animation
                setTimeout(() => {
                  cardElement.classList.remove("task-highlight-fade-out");
                }, 500);
              }, 3000);
            } else {
              // Fallback: apply to the wrapper element
              fieldElement.classList.add("task-highlight");

              setTimeout(() => {
                fieldElement.classList.remove("task-highlight");
                fieldElement.classList.add("task-highlight-fade-out");

                setTimeout(() => {
                  fieldElement.classList.remove("task-highlight-fade-out");
                }, 500);
              }, 3000);
            }
          }
        }, 300);
      }
    };

    // Switch to the correct phase if not already selected
    if (selectedPhase !== normalizedPhaseName) {
      handlePhaseSelect(normalizedPhaseName);

      // Wait for fields to load by checking if they exist
      const waitForFields = () => {
        const checkFields = () => {
          const fieldElement = document.getElementById(`field-${taskId}`);

          if (fieldElement) {
            // Fields are loaded, proceed with scroll and highlight
            scrollAndHighlight();
          } else {
            // Fields not loaded yet, check again in 100ms
            setTimeout(checkFields, 100);
          }
        };

        // Start checking after a short delay to allow phase switch
        setTimeout(checkFields, 200);
      };

      waitForFields();
    } else {
      // Already in the correct phase, proceed immediately
      scrollAndHighlight();
    }
  };

  const handleSave = async () => {
    try {
      if (!editMode) {
        setEditMode(true);

        return;
      }

      if (
        Object.keys(updatedFields).length === 0 &&
        Object.keys(updatedObservations).length === 0
      ) {
        setEditMode(false);

        return;
      }

      // Transform updated fields to API format
      const fieldsToUpdate = transformComponentFieldsToApi(
        updatedFields,
        updatedObservations,
        fieldsData?.fields || [],
      );

      // Update fields via API - only send the updated fields
      await updateFields(fieldsToUpdate);

      setUpdatedFields({});
      setUpdatedObservations({});
      setEditMode(false);
    } catch (error) {
      console.error("Error saving fields:", error);
    }
  };

  const cancelSave = () => {
    setCancelEditTrigger(!cancelEditTrigger);
    setEditMode(false);
  };

  // These were moved to the top of the component

  const subphaseCompletition = (subphase: Subphase) => {
    // Use the percentage from the API data
    const percentage = subphase.percentage;

    return (
      <Chip
        color={
          percentage === 100 ? "success" : percentage > 0 ? "warning" : "danger"
        }
        size="sm"
        variant="solid"
      >
        {percentage === 100 ? `${percentage}%` : `${percentage}%`}
      </Chip>
    );
  };

  // This was moved to the top of the component

  // These were moved to the top of the component

  // Show loading state only for project data (we need project data to render the page structure)
  if (projectLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse">Cargando proyecto...</div>
      </div>
    );
  }

  // Show error state
  if (projectError || fieldsError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">Error: {projectError || fieldsError}</div>
      </div>
    );
  }

  // Show empty state if no data
  if (!projectData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div>No se encontró el proyecto</div>
      </div>
    );
  }

  // This was moved to the top of the component

  // Skeleton component for fields loading
  const FieldsSkeleton = () => (
    <div className="mt-2 space-y-4">
      {[1, 2, 3].map((item) => (
        <React.Fragment key={item}>
          <div className="m-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Skeleton className="h-6 w-48 rounded-lg" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-4 rounded" />
            </div>
            <div className="space-y-3">
              {[1, 2, 3, 4].map((fieldIndex) => (
                <Card key={fieldIndex} className="shadow-sm">
                  <CardBody className="p-4">
                    <div className="flex items-start gap-4">
                      <Skeleton className="h-4 w-4 rounded mt-1" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-5 w-3/4 rounded-lg" />
                        <Skeleton className="h-4 w-full rounded-lg" />
                        <Skeleton className="h-10 w-full rounded-lg" />
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </React.Fragment>
      ))}
    </div>
  );

  return (
    <div>
      {/* Sticky header section */}
      <div className="sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-divider pb-4">
        <div className="flex justify-between items-center mx-auto mb-4">
          <div className="flex items-center gap-3">
            <h2 className={title({ size: "sm" })}>
              {projectData.project.lid} - {projectData.project.alias}
            </h2>
            <div
              className={`text-center p-1 rounded-lg ${getPhaseStyle(
                currentPhaseName,
                theme === "dark",
              )}`}
            >
              {currentPhaseName}
            </div>
            <div className="flex justify-center">
              {tempState === "SCHEDULED" ? (
                <Icon icon="lucide:clock" width={18} />
              ) : tempState === "IN_PROGRESS" ? (
                <Icon icon="lucide:play-circle" width={18} />
              ) : tempState === "ON_HOLD" ? (
                <Icon icon="lucide:pause-circle" width={18} />
              ) : tempState === "CANCELLED" ? (
                <Icon icon="lucide:x-circle" width={18} />
              ) : (
                <Icon icon="lucide:circle" width={18} />
              )}
            </div>
            <div className="text-xs text-default-500 font-bold mt-2">
              Creado:&nbsp;
              {projectData.project.created_at
                ? new Date(projectData.project.created_at).toLocaleDateString(
                    "es-ES",
                    {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    },
                  )
                : "N/A"}
              {/* {formatDate(reportDetail.created_at)} */}
            </div>
          </div>
          <div className="flex gap-2">
            <Tooltip
              content={"Notificaciones enviadas"}
              placement="top"
              showArrow={true}
            >
              <Button
                isIconOnly
                color="primary"
                startContent={<FaEnvelope />}
                variant="solid"
                onPress={() => setMailModalOpen(true)}
              />
            </Tooltip>
            <Tooltip
              content={"Ver tareas pendientes"}
              placement="top"
              showArrow={true}
            >
              <Button
                isIconOnly
                color="primary"
                startContent={<FaTasks />}
                variant="solid"
                onPress={() => setTasksModalOpen(true)}
              />
            </Tooltip>
            <Tooltip content={"Verificar fases"} placement="top" showArrow={true}>
              <Button
                isIconOnly
                color="primary"
                isDisabled={!canVerifyPhase}
                startContent={<Icon icon="lucide:circle-check-big" />}
                variant="solid"
                onPress={() => setIsVerificationModalOpen(true)}
              />
            </Tooltip>
            <Tooltip content={"Ver contactos"} placement="top" showArrow={true}>
              <Button
                isIconOnly
                color="primary"
                isDisabled={!canViewContacts}
                startContent={<Icon icon="lucide:users" />}
                variant="solid"
                onPress={() => setIsOpen(true)}
              />
            </Tooltip>
            <Tooltip
              content={"Generar reporte"}
              placement="bottom-end"
              showArrow={true}
            >
              <Button
                isIconOnly
                color="primary"
                startContent={<Icon icon="lucide:file-text" />}
                variant="solid"
                onPress={() => setReportModalOpen(true)}
              />
            </Tooltip>
          </div>
        </div>

        <Card className="mx-auto" radius={"sm"}>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-9 gap-4">
              <div>
                <h3 className="text-sm text-default-500">Agregador:</h3>
                <p className="text-sm">
                  {projectData.project.aggregator || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Tipología:</h3>
                <p className="text-sm">
                  {projectData.project.implementation_type || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Mes Live:</h3>
                <p className="text-sm">
                  {projectData.dates.golive_final_date
                    ? new Date(
                        projectData.dates.golive_final_date,
                      ).toLocaleDateString("es-ES", {
                        month: "2-digit",
                        year: "numeric",
                      })
                    : "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Tests:</h3>
                <p className="text-sm">
                  {[projectData.dates.month1_test, projectData.dates.month2_test]
                    .filter(Boolean)
                    .map((date) =>
                      new Date(date!).toLocaleDateString("es-ES", {
                        month: "2-digit",
                        year: "numeric",
                      }),
                    )
                    .join(", ") || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Coordinador:</h3>
                <p className="text-sm">
                  {projectData.project.coordinator || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Implementador 1:</h3>
                <p className="text-sm">
                  {projectData.project.implementer1 || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Implementador 2:</h3>
                <p className="text-sm">
                  {projectData.project.implementer2 || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Incubadora:</h3>
                <p className="text-sm">
                  {projectData.project.incubator || "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-sm text-default-500">Backup:</h3>
                <p className="text-sm">{projectData.project.backup || "N/A"}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Progress Timeline - also sticky */}
        <ProgressTimeline
          isEditMode={editMode}
          projectData={projectData}
          selectedPhase={selectedPhase}
          onPhaseSelect={handlePhaseSelect}
        />
      </div>
      {/* {JSON.stringify(updatedFields)} */}

      {fieldsLoading ? (
        <FieldsSkeleton />
      ) : (
        <Accordion
          aria-label="Subphases accordion"
          className="mt-2"
          defaultSelectedKeys={"all"}
          itemClasses={{
            title: "text-2xl font-bold",
          }}
          selectedKeys={selectedSubphases}
          selectionMode="multiple"
          onSelectionChange={handleSelectionChange}
        >
          {/* filtro por milestone */}
          {filteredSubphases
            .filter((subphase) => {
              if (subphase.fields.every((field) => field.is_active === false))
                return false;
              else return true;
            })
            .sort((a, b) => a.order - b.order)
            .map((subphase) => (
              <AccordionItem
                key={subphase.id}
                aria-label={`Subfase ${subphase.order}`}
                className="w-full mb-4"
                title={
                  <div className="flex items-center gap-2">
                    <span>{subphase.title}</span>
                    {subphaseCompletition(subphase)}
                  </div>
                }
              >
                {subphase.fields
                  .filter((field) => {
                    if (field.is_active === false) {
                      return false; // Skip inactive fields
                    }

                    return true;
                  })
                  .filter((field) => {
                    if (filterMilestones) {
                      return !field.milestone;
                    }

                    return true;
                  })
                  .filter((field) => {
                    if (filterCompleted) {
                      return !isFieldCompleted(field);
                    }

                    return true;
                  })
                  .sort((a, b) => {
                    // First sort by hierarchy (ascending)
                    const hierarchyA = a.hierarchy || 0;
                    const hierarchyB = b.hierarchy || 0;

                    if (hierarchyA !== hierarchyB) {
                      return hierarchyA - hierarchyB;
                    }

                    // If hierarchy is the same, sort alphabetically by title
                    return a.title.localeCompare(b.title);
                  })
                  .map((field) => (
                    <MemoizedField key={field.id} field={field} />
                  ))}
              </AccordionItem>
            ))}
        </Accordion>
      )}

      <CommentsSection
        currentPhase={String(
          getPhaseIdByName(projectData.phases, selectedPhase) || "",
        )}
        projectId={projectId}
      />

      <NotificationsModal
        isOpen={mailModalOpen}
        selectedProject={Number(projectId)}
        onClose={() => setMailModalOpen(false)}
      />

      <ContactsModal
        isOpen={isOpen}
        selectedProject={Number(projectId)}
        onClose={() => setIsOpen(false)}
      />

      <TasksModal
        isOpen={tasksModalOpen}
        tasks={pendingFields}
        onClose={() => setTasksModalOpen(false)}
        onViewTask={handleViewTask}
      />

      <VerificationModal
        isOpen={isVerificationModalOpen}
        projectData={projectData}
        onClose={() => setIsVerificationModalOpen(false)}
        onPhaseVerified={refetchProject}
      />

      <ReportModal
        isOpen={reportModalOpen}
        projectId={projectId}
        onClose={() => setReportModalOpen(false)}
      />

      {editMode ? (
        <Button
          className="fixed bottom-4 right-20 z-50"
          color="danger"
          size="lg"
          variant="light"
          onPress={cancelSave}
        >
          Cancelar
        </Button>
      ) : null}

      <Button
        isIconOnly
        className="fixed bottom-4 right-4 z-50"
        color="primary"
        isDisabled={!canModifyFields}
        isLoading={fieldsUpdating}
        size="lg"
        startContent={
          !fieldsUpdating && (
            <Icon icon={editMode ? "lucide:save" : "lucide:edit-3"} />
          )
        }
        variant="solid"
        onPress={handleSave}
      />

      <div
        className={`fixed bottom-32 right-[-65px] z-50 min-w-6 min-h-32 ${
          theme === "dark" ? "bg-gray-800" : "bg-slate-600"
        } bg-opacity-75 rounded-lg shadow-lg p-4 hover:right-[-10px] transition-all duration-300 flex flex-col items-center gap-3`}
      >
        <Button
          isIconOnly
          color="danger"
          size="lg"
          startContent={
            filterMilestones ? (
              <svg
                className="lucide lucide-milestone-icon"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 13v8" />
                <path d="M12 3v3" />
                <path d="M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z" />
              </svg>
            ) : (
              <svg
                className="lucide lucide-milestone-icon"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 13v8" />
                <path d="M12 3v3" />
                <path d="M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z" />
                <path d="M3 3l18 18" stroke="white" strokeWidth="4" />
                <path d="M3 3l18 18" />
              </svg>
            )
          }
          variant="solid"
          onPress={() => setFilterMilestones(!filterMilestones)}
        />

        <Button
          isIconOnly
          color="primary"
          size="lg"
          startContent={
            !filterCompleted ? (
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path d="M3 3l18 18" stroke="white" strokeWidth="4" />
                <path d="M3 3l18 18" />
              </svg>
            ) : (
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )
          }
          variant="solid"
          onPress={() => setFilterCompleted(!filterCompleted)}
        />
      </div>
    </div>
  );
}
